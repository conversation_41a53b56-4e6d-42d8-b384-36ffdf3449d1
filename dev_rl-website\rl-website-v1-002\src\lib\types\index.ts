/**
 * Central export file for all type definitions in the application
 *
 * This file exports all types from the lib/types directory,
 * providing a single import point for all type definitions.
 */

// Content data models
export * from './content';

// Component props
export * from './components';

// Common utility types
export interface Dictionary<T> {
  [key: string]: T;
}

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
