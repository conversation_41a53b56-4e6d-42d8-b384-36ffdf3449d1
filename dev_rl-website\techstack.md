# Technology Stack

## Core Framework
- **React 18.3.1** - Component-based UI library
- **TypeScript 5.5.3** - Type-safe JavaScript superset
- **Vite 5.4.2** - Modern build tool and dev server

## Styling & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Framer Motion 12.5.0** - Animation library
- **Lucide React 0.344.0** - Icon library
- **PostCSS 8.4.35** - CSS processing

## Routing & Navigation
- **React Router DOM 6.22.3** - Client-side routing

## Development Tools
- **ESLint 9.9.1** - Code linting
- **TypeScript ESLint 8.3.0** - TypeScript-specific linting

## Deployment
- **Vercel** - Hosting platform (configured via vercel.json)

## Project Structure
- **Component-based architecture** with organized sections
- **Path aliasing** (@/* → src/*)
- **Environment-based builds** (dev/staging/production)
