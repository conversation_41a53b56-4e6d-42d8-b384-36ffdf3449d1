/**
 * Contact Information Analysis Script
 * 
 * This script scans the codebase for contact information references (phone numbers, emails, etc.)
 * and generates a report of all occurrences, helping to identify where consolidation is needed.
 */

const fs = require('fs');
const path = require('path');

// Patterns to search for
const PATTERNS = {
  // Norwegian phone number formats
  phoneNumbers: [
    /\+47\s*\d{2}\s*\d{2}\s*\d{2}\s*\d{2}/g,  // +47 12 34 56 78
    /\+47\s*\d{3}\s*\d{2}\s*\d{3}/g,          // +47 123 45 678
    /\+47\d{8}/g,                             // +4712345678
    /\(\+47\)\s*\d{8}/g,                      // (+47) 12345678
    /\d{2}\s*\d{2}\s*\d{2}\s*\d{2}/g,         // 12 34 56 78
    /\d{3}\s*\d{2}\s*\d{3}/g,                 // 123 45 678
    /\d{8}/g                                  // 12345678 (risky, might catch other numbers)
  ],
  
  // Email patterns
  emails: [
    /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
  ],
  
  // Address patterns (more complex, might have false positives)
  addresses: [
    /\d+\s+[A-Za-zæøåÆØÅ\s]+(?:vei|gate|gata|veien)/gi,  // Street addresses
    /\d{4}\s+[A-Za-zæøåÆØÅ\s]+/g                         // Postal code + city
  ],
  
  // Social media patterns
  socialMedia: [
    /https?:\/\/(?:www\.)?facebook\.com\/[A-Za-z0-9_.-]+/g,
    /https?:\/\/(?:www\.)?instagram\.com\/[A-Za-z0-9_.-]+/g,
    /https?:\/\/(?:www\.)?linkedin\.com\/[A-Za-z0-9_.-]+/g
  ]
};

// File extensions to scan
const EXTENSIONS_TO_SCAN = ['.tsx', '.ts', '.jsx', '.js', '.md', '.json', '.html'];

// Directories to exclude
const EXCLUDE_DIRS = ['node_modules', 'dist', '.git', 'scripts'];

// Results storage
const results = {
  totalOccurrences: 0,
  fileOccurrences: {},
  patternCounts: {
    phoneNumbers: 0,
    emails: 0,
    addresses: 0,
    socialMedia: 0
  }
};

/**
 * Scans a file for contact information references
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fileHasOccurrences = false;
    const fileOccurrences = [];

    // Check each pattern type
    for (const [patternType, patterns] of Object.entries(PATTERNS)) {
      patterns.forEach(pattern => {
        // Reset the pattern's lastIndex to ensure we start from the beginning
        if (pattern.global) pattern.lastIndex = 0;
        
        const matches = content.match(pattern);
        
        if (matches && matches.length > 0) {
          fileHasOccurrences = true;
          results.patternCounts[patternType] += matches.length;
          results.totalOccurrences += matches.length;
          
          // Find line numbers for each occurrence
          const lines = content.split('\n');
          const occurrences = [];
          
          lines.forEach((line, index) => {
            // Reset the pattern's lastIndex for each line
            if (pattern.global) pattern.lastIndex = 0;
            
            if (pattern.test(line)) {
              occurrences.push({
                line: index + 1,
                content: line.trim()
              });
            }
          });
          
          fileOccurrences.push({
            patternType,
            pattern: pattern.toString(),
            count: matches.length,
            occurrences
          });
        }
      });
    }
    
    if (fileHasOccurrences) {
      results.fileOccurrences[filePath] = fileOccurrences;
    }
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scans a directory for files to analyze
 */
function scanDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(entry.name)) {
          scanDirectory(fullPath);
        }
      } else if (entry.isFile()) {
        // Only scan files with specified extensions
        const ext = path.extname(entry.name).toLowerCase();
        if (EXTENSIONS_TO_SCAN.includes(ext)) {
          scanFile(fullPath);
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

// Start the scan from the src directory
console.log('Starting contact information analysis...');
const startTime = Date.now();

// Get the root directory
const rootDir = path.resolve(__dirname, '../../src');
scanDirectory(rootDir);

// Generate the report
const endTime = Date.now();
const scanDuration = ((endTime - startTime) / 1000).toFixed(2);

console.log(`\nContact Information Analysis Complete (${scanDuration}s)`);
console.log(`Total occurrences found: ${results.totalOccurrences}`);
console.log('\nPattern Counts:');
Object.entries(results.patternCounts).forEach(([pattern, count]) => {
  console.log(`  ${pattern}: ${count}`);
});

console.log('\nFiles with Contact Information:');
Object.keys(results.fileOccurrences).forEach(filePath => {
  const relPath = path.relative(rootDir, filePath);
  const totalInFile = results.fileOccurrences[filePath].reduce((sum, item) => sum + item.count, 0);
  console.log(`  ${relPath} (${totalInFile} occurrences)`);
});

// Write detailed results to a JSON file
const outputPath = path.join(__dirname, 'contact-references-report.json');
fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
console.log(`\nDetailed report written to: ${outputPath}`);

// Generate a Markdown report for easier reading
const markdownPath = path.join(__dirname, 'contact-references-report.md');
let markdown = '# Contact Information Analysis Report\n\n';
markdown += `Analysis completed in ${scanDuration} seconds\n\n`;
markdown += `## Summary\n\n`;
markdown += `- Total occurrences: ${results.totalOccurrences}\n\n`;

markdown += `## Pattern Counts\n\n`;
markdown += `| Pattern Type | Count |\n`;
markdown += `|-------------|-------|\n`;
Object.entries(results.patternCounts).forEach(([pattern, count]) => {
  markdown += `| ${pattern} | ${count} |\n`;
});

markdown += `\n## Files with Contact Information\n\n`;
Object.keys(results.fileOccurrences).forEach(filePath => {
  const relPath = path.relative(rootDir, filePath);
  markdown += `\n### ${relPath}\n\n`;
  
  results.fileOccurrences[filePath].forEach(item => {
    markdown += `#### ${item.patternType} (${item.count} occurrences)\n\n`;
    markdown += `Pattern: \`${item.pattern}\`\n\n`;
    markdown += `| Line | Content |\n`;
    markdown += `|------|--------|\n`;
    
    item.occurrences.forEach(occurrence => {
      // Escape pipe characters in the content
      const escapedContent = occurrence.content.replace(/\|/g, '\\|');
      markdown += `| ${occurrence.line} | \`${escapedContent}\` |\n`;
    });
    
    markdown += '\n';
  });
});

fs.writeFileSync(markdownPath, markdown);
console.log(`Markdown report written to: ${markdownPath}`);
