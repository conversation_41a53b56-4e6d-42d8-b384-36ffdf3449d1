import { IMAGE_PATHS, IMAGE_CATEGORIES } from '@/lib/config/images';

// Re-export IMAGE_CATEGORIES for use in other modules
export { IMAGE_CATEGORIES };

/**
 * Constants and utilities for working with images throughout the application
 */

export interface ImageFile {
  filename: string;
  path: string;
  category: string;
  metadata?: {
    title?: string;
    description?: string;
    location?: string;
    date?: string;
  };
}

/**
 * Converts an image path to its WebP version
 *
 * @param imagePath The original image path
 * @returns The WebP version of the image path
 */
export const getWebPVersion = (imagePath: string): string => {
  // If already a WebP image, return as is
  if (imagePath.endsWith(".webp")) {
    return imagePath;
  }

  // For external URLs (like Unsplash), return original
  if (imagePath.startsWith("http")) {
    return imagePath;
  }

  // Create WebP path by replacing extension
  return imagePath.replace(/\.(jpe?g|png|gif)$/i, ".webp");
};

/**
 * Interface for image with geocoordinates
 */
export interface GeoImage extends ImageFile {
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Extracts geocoordinates from a filename if present
 *
 * @param filename The filename that may contain geocoordinates
 * @returns An object with the base filename and coordinates if found
 */
export const extractGeoCoordinates = (filename: string): {
  baseFilename: string;
  coordinates?: { latitude: number; longitude: number }
} => {
  // Match pattern like IMG_0035_59.923192_10.649406.webp
  const geoPattern = /^(.+)_(\d+\.\d+)_(\d+\.\d+)\.(\w+)$/;
  const match = filename.match(geoPattern);

  if (match) {
    const [, baseFilename, latitude, longitude, extension] = match;
    return {
      baseFilename: `${baseFilename}.${extension}`,
      coordinates: {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude)
      }
    };
  }

  return { baseFilename: filename };
};

/**
 * Gets all images from a specific category, handling geocoordinate filenames
 *
 * @param category The category to get images from
 * @returns An array of image files
 */
export const getImagesFromCategory = (category: keyof typeof IMAGE_CATEGORIES): GeoImage[] => {
  const categoryPath = IMAGE_PATHS.categories[category];

  // Fallback image lists in case we can't dynamically load them
  // For belegg, we're using the new format with geocoordinates
  // For other categories, we're keeping the original format for now
  const fallbackImages: Record<string, string[]> = {
    belegg: [
      'IMG_0035_59.923192_10.649406.webp',
      'IMG_0085_60.163047_10.252728.webp',
      'IMG_0121_60.169094_10.276781.webp',
      'IMG_0129_60.169103_10.276797.webp',
      'IMG_0208_59.932006_10.478197.webp',
      'IMG_0451_60.274486_10.186981.webp',
      'IMG_0453_60.274647_10.187028.webp',
      'IMG_0715.webp',
      'IMG_0717.webp',
      'IMG_1935_59.912947_10.575681.webp',
      'IMG_2941_60.181500_10.273881.webp',
      'IMG_3001_60.181519_10.274283.webp'
    ],
    hekk: [
      'hekk_20.webp', 'IMG_0167.webp', 'IMG_1841.webp', 'IMG_2370.webp',
      'IMG_2371.webp', 'IMG_3077.webp'
    ],
    stål: [
      'IMG_0068.webp', 'IMG_0069.webp', 'IMG_1916.webp', 'IMG_1917.webp',
      'IMG_1918.webp', 'IMG_2441.webp', 'IMG_3599.webp', 'IMG_3602.webp',
      'IMG_3847.webp'
    ],
    støttemur: [
      'IMG_0144.webp', 'IMG_0318.webp', 'IMG_0324.webp', 'IMG_0325.webp',
      'IMG_0452.webp', 'IMG_0932.webp', 'IMG_2855.webp'
    ],
    'trapp-repo': [
      'IMG_0295.webp', 'IMG_0401.webp', 'IMG_0448.webp', 'IMG_0449.webp',
      'IMG_0450.webp', 'IMG_1081.webp', 'IMG_4111.webp'
    ],
    kantstein: [
      'IMG_0066.webp', 'IMG_0364.webp', 'IMG_0369.webp', 'IMG_0427.webp',
      'IMG_0429.webp', 'IMG_0445.webp', 'IMG_0716.webp'
    ],
    ferdigplen: [
      'IMG_0071.webp', 'IMG_1912.webp'
    ],
    platting: [
      'IMG_3251.webp', 'IMG_4188.webp'
    ]
  };

  // For now, we'll use the fallback images synchronously
  // In the future, this could be converted to an async function
  const images = fallbackImages[category] || [];

  return images.map(filename => {
    // Extract geocoordinates if present
    const { coordinates } = extractGeoCoordinates(filename);

    // Create the image object
    const imageObject: GeoImage = {
      filename,
      path: `${categoryPath}/${filename}`,
      category: IMAGE_CATEGORIES[category],
      metadata: {
        title: `${IMAGE_CATEGORIES[category]} prosjekt`,
        description: `Profesjonell utførelse av ${IMAGE_CATEGORIES[category].toLowerCase()}`
      }
    };

    // Add coordinates if available
    if (coordinates) {
      imageObject.coordinates = coordinates;

      // Add location to metadata if coordinates are available
      if (imageObject.metadata) {
        imageObject.metadata.location = `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
      }
    }

    return imageObject;
  });
};

/**
 * Gets the optimal image size for a given viewport width
 *
 * @param viewportWidth The current viewport width
 * @returns The optimal image size
 */
export const getOptimalImageSize = (viewportWidth: number): string => {
  if (viewportWidth < 640) return 'sm';
  if (viewportWidth < 1024) return 'md';
  if (viewportWidth < 1536) return 'lg';
  return 'xl';
};

/**
 * Finds the correct image filename in a category directory, handling geocoordinate filenames
 *
 * @param category The category directory (e.g., 'belegg', 'stål')
 * @param baseFilename The base filename without geocoordinates (e.g., 'IMG_3037')
 * @returns The full image path with correct filename
 */
export const getImagePathWithFallback = (category: string, baseFilename: string): string => {
  // Get the fallback images for this category
  const fallbackImages: Record<string, string[]> = {
    belegg: [
      'IMG_0035_59.923192_10.649406.webp',
      'IMG_0085_60.163047_10.252728.webp',
      'IMG_0121_60.169094_10.276781.webp',
      'IMG_0129_60.169103_10.276797.webp',
      'IMG_0208_59.932006_10.478197.webp',
      'IMG_0451_60.274486_10.186981.webp',
      'IMG_0453_60.274647_10.187028.webp',
      'IMG_0715.webp',
      'IMG_0717.webp',
      'IMG_1935_59.912947_10.575681.webp',
      'IMG_2941_60.181500_10.273881.webp',
      'IMG_3001_60.181519_10.274283.webp',
      'IMG_3021_60.181469_10.274167.webp',
      'IMG_3023_60.181544_10.274206.webp',
      'IMG_3033_60.181594_10.274214.webp',
      'IMG_3034_60.181569_10.274150.webp',
      'IMG_3035_60.181569_10.274142.webp',
      'IMG_3036_60.181564_10.274239.webp',
      'IMG_3037_60.181492_10.274272.webp'
    ],
    hekk: [
      'hekk_20.webp', 'IMG_0167.webp', 'IMG_1841.webp', 'IMG_2370.webp',
      'IMG_2371.webp', 'IMG_3077.webp'
    ],
    stål: [
      'IMG_0068.webp', 'IMG_0069.webp', 'IMG_1916.webp', 'IMG_1917.webp',
      'IMG_1918.webp', 'IMG_2441.webp', 'IMG_3599.webp', 'IMG_3602.webp',
      'IMG_3847.webp'
    ],
    støttemur: [
      'IMG_0144.webp', 'IMG_0318.webp', 'IMG_0324.webp', 'IMG_0325.webp',
      'IMG_0452.webp', 'IMG_0932.webp', 'IMG_2855.webp'
    ],
    'trapp-repo': [
      'IMG_0295.webp', 'IMG_0401.webp', 'IMG_0448.webp', 'IMG_0449.webp',
      'IMG_0450.webp', 'IMG_1081.webp', 'IMG_4111.webp'
    ],
    kantstein: [
      'IMG_0066.webp', 'IMG_0364.webp', 'IMG_0369.webp', 'IMG_0427.webp',
      'IMG_0429.webp', 'IMG_0445.webp', 'IMG_0716.webp'
    ],
    ferdigplen: [
      'IMG_0071.webp', 'IMG_1912.webp'
    ],
    platting: [
      'IMG_3251.webp', 'IMG_4188.webp'
    ]
  };

  // Extract the base filename without extension
  const baseNameWithoutExt = baseFilename.replace(/\.\w+$/, '');

  // Find the matching file in the fallback images
  const matchingFile = fallbackImages[category]?.find((filename: string) =>
    filename.startsWith(baseNameWithoutExt + '_') || filename === baseFilename
  );

  // If a matching file is found, use it; otherwise, use the original filename
  const finalFilename = matchingFile || baseFilename;

  // Return the full path
  return `/images/categorized/${category}/${finalFilename}`;
};