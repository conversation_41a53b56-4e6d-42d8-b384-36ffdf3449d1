import { ProjectType } from '../../lib/types';
import { recentProjects } from '../../data/projects';

/**
 * Export project data for use throughout the application
 */
export const projects: ProjectType[] = recentProjects;

/**
 * Get a project by ID
 * @param id Project ID
 * @returns Project or undefined if not found
 */
export function getProjectById(id: string): ProjectType | undefined {
  return projects.find(project => project.id === id);
}

/**
 * Get featured projects
 * @returns Array of featured projects
 */
export function getFeaturedProjects(): ProjectType[] {
  return projects.filter(project => project.featured);
}

/**
 * Get projects by category
 * @param category Project category
 * @returns Array of projects in the specified category
 */
export function getProjectsByCategory(category: string): ProjectType[] {
  return projects.filter(project => project.category === category);
}

/**
 * Get projects by tag
 * @param tag Project tag
 * @returns Array of projects with the specified tag
 */
export function getProjectsByTag(tag: string): ProjectType[] {
  return projects.filter(project => project.tags.includes(tag));
}

/**
 * Get all project categories
 * @returns Array of unique project categories
 */
export function getProjectCategories(): string[] {
  return [...new Set(projects.map(project => project.category))];
}

/**
 * Get all project tags
 * @returns Array of unique project tags
 */
export function getProjectTags(): string[] {
  return [...new Set(projects.flatMap(project => project.tags))];
}
