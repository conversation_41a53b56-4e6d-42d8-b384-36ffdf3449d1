# Contact Form Section

This section contains the implementation of the Ringerike Landskap contact form with advanced analytics and SEO tracking capabilities.

## Overview

The contact form serves as the primary conversion point for the website, allowing users to submit inquiries, request consultations, or provide feedback. Beyond its basic functionality, the form has been enhanced with comprehensive client information tracking to provide valuable insights for marketing and SEO analysis.

## Key Features

1. **Form Validation**: Client-side validation with error messages
2. **Submission Handling**: Integration with Formspree.io for form processing
3. **Success/Error Feedback**: Clear user feedback on submission status
4. **Comprehensive Analytics**: Collection of detailed user and session data
5. **SEO Optimization**: Structured data collection for marketing insights

## Analytics Implementation

The contact form collects and submits the following categories of data:

### Basic Client Information
- Device type, OS, browser, screen size
- Country and region detection
- Local time in Norwegian format (YYYY.MM.DD, Kl.HH:MM)

### Performance Metrics
- Page load time
- First Contentful Paint (FCP)
- Largest Contentful Paint (LCP)
- Time to Interactive (TTI)
- Cumulative Layout Shift (CLS)

### User Behavior Data
- Time on site
- Session duration
- Pages viewed count
- Bounce rate indicator
- Max scroll depth
- Navigation path

### Engagement Metrics
- Total interactions count
- CTA clicks
- Form interactions
- Time to first interaction

### Conversion Data
- Conversion path
- Time to conversion
- Returning visitor conversion indicator
- Previous sites visited

### Marketing Data
- UTM parameters (source, medium, campaign, term, content)
- Search keywords detection

## Technical Implementation

The implementation uses:

- React hooks for state management
- Browser APIs for data collection
- localStorage/sessionStorage for persistent tracking
- Event listeners for user interaction tracking
- Norwegian date formatting

## Usage

The contact form is designed to be used as a standalone section on the contact page. It requires no props and manages its own state internally.

```tsx
import ContactSection from '@/sections/60-contact';

function ContactPage() {
  return (
    <main>
      <ContactSection />
    </main>
  );
}
```

## Form Submission Process

1. User fills out the form fields
2. Client-side validation is performed on blur and submit
3. On submission, client information is collected
4. Data is sent to Formspree.io endpoint
5. Success/error state is displayed based on the response
6. Form is reset on successful submission

## Data Privacy Considerations

- All data is collected client-side and only sent when the user explicitly submits the form
- No personally identifiable information is collected beyond what the user provides
- Data collection complies with GDPR requirements

## Related Documentation

For more detailed information about the analytics implementation, see the [Contact Form Analytics Documentation](../../../docs/CONTACT_FORM_ANALYTICS.md).

## Maintenance Notes

When updating the contact form:

1. Ensure all validation logic remains intact
2. Test form submissions thoroughly
3. Verify that all analytics data is being collected correctly
4. Maintain the Norwegian date format (YYYY.MM.DD, Kl.HH:MM)
5. Ensure proper error handling for API calls
