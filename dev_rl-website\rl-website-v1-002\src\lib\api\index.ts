/**
 * API utilities for fetching and managing data
 *
 * This module provides a centralized way to handle data fetching,
 * allowing us to easily switch between static data, local API,
 * or external API in the future.
 */

import { services } from '../../data/services';
import { recentProjects } from '../../data/projects';
import { testimonials } from '../../data/testimonials';
import { SERVICE_AREAS } from '../constants';
import { ProjectType, ServiceType, TestimonialType, ServiceArea } from '../types';
import { getCurrentSeason } from '../utils';
import { normalizeString, normalizedStringCompare } from '../utils/strings';

// Map seasons to relevant project categories and tags
const SEASONAL_PROJECTS = {
  'vår': {
    categories: ['Hekk og Beplantning', 'Ferdigplen'],
    tags: ['beplantning', 'plen', 'hage']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkjørsel']
  },
  'høst': {
    categories: ['<PERSON>øttemur', '<PERSON><PERSON><PERSON>', 'Trapper og Repoer'],
    tags: ['terrengforming', 'støttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenstål', 'Støttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
};

// Map seasons to relevant service categories and features
const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning'],
    features: ['Planting', 'Vanning', 'Gjødsling', 'Jordarbeid']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
};

/**
 * Get all services
 */
export const getServices = async (): Promise<ServiceType[]> => {
  // In the future, this could fetch from an API
  return services;
};

/**
 * Get a service by ID
 */
export const getServiceById = async (id: string): Promise<ServiceType | undefined> => {
  return services.find(service => service.id === id);
};

/**
 * Get services filtered by criteria
 */
export const getFilteredServices = async (
  category?: string,
  feature?: string,
  season?: string
): Promise<ServiceType[]> => {
  // Apply filters in a hierarchical way: first season, then category, then feature
  let filtered = [...services];

  // Step 1: Filter by season first (primary filter)
  if (season) {
    const seasonMapping = SEASONAL_SERVICES[season as keyof typeof SEASONAL_SERVICES];
    if (seasonMapping) {
      filtered = filtered.filter(service => {
        // Extract the main category from the service title
        const serviceMainCategory = service.title.split(' ')[0];

        // Check if the service's main category is explicitly included in the season's categories
        const exactCategoryMatch = seasonMapping.categories.some(cat => {
          const normalizedServiceCategory = normalizeString(serviceMainCategory);
          const normalizedCategory = normalizeString(cat);

          return normalizedServiceCategory === normalizedCategory ||
                 normalizeString(service.title).startsWith(normalizedCategory);
        });

        if (exactCategoryMatch) {
          return true;
        }

        // Check for partial category match in title
        const partialCategoryMatch = seasonMapping.categories.some(cat => {
          const normalizedServiceTitle = normalizeString(service.title);
          const normalizedCategory = normalizeString(cat);

          return normalizedServiceTitle.includes(normalizedCategory);
        });

        // Check for feature match with season-specific features
        const featureMatch = service.features?.some(feat =>
          seasonMapping.features.some(seasonFeat => {
            const normalizedFeature = normalizeString(feat);
            const normalizedSeasonFeature = normalizeString(seasonFeat);

            return normalizedFeature.includes(normalizedSeasonFeature);
          })
        ) || false;

        return partialCategoryMatch || featureMatch;
      });
    }
  }

  // Step 2: Filter by category (secondary filter)
  if (category) {
    filtered = filtered.filter(service => {
      const normalizedServiceTitle = normalizeString(service.title);
      const normalizedCategory = normalizeString(category);
      const normalizedServiceId = normalizeString(service.id);

      return normalizedServiceTitle.includes(normalizedCategory) ||
             normalizedServiceId === normalizedCategory;
    });
  }

  // Step 3: Filter by feature (tertiary filter - only filters within already filtered results)
  if (feature && filtered.length > 0) {
    filtered = filtered.filter(service =>
      service.features?.some(f => {
        const normalizedFeature = normalizeString(f);
        const normalizedFilterFeature = normalizeString(feature);

        return normalizedFeature.includes(normalizedFilterFeature);
      })
    );
  }

  return filtered;
};

/**
 * Get seasonal services based on current season
 */
export const getSeasonalServices = async (limit?: number): Promise<ServiceType[]> => {
  const currentSeason = getCurrentSeason();
  const filtered = await getFilteredServices(undefined, undefined, currentSeason);
  return limit ? filtered.slice(0, limit) : filtered;
};

/**
 * Get all projects
 */
export const getProjects = async (): Promise<ProjectType[]> => {
  return recentProjects;
};

/**
 * Get a project by ID
 */
export const getProjectById = async (id: string): Promise<ProjectType | undefined> => {
  return recentProjects.find(project => project.id === id);
};

/**
 * Get projects filtered by criteria
 */
export const getFilteredProjects = async (
  category?: string,
  location?: string,
  tag?: string,
  season?: string
): Promise<ProjectType[]> => {
  let filtered = [...recentProjects];

  if (category) {
    filtered = filtered.filter(project => {
      // Use normalized string comparison to handle Norwegian characters and variations
      const normalizedProjectCategory = normalizeString(project.category);
      const normalizedCategory = normalizeString(category);

      // Check if one contains the other (handles singular/plural variations)
      return normalizedProjectCategory.includes(normalizedCategory) ||
             normalizedCategory.includes(normalizedProjectCategory);
    });
  }

  if (location) {
    filtered = filtered.filter(project => {
      // Use normalized string comparison for location
      return normalizedStringCompare(project.location, location);
    });
  }

  if (tag) {
    filtered = filtered.filter(project => {
      // Use normalized string comparison for tags
      const normalizedTag = normalizeString(tag);
      return project.tags.some(projectTag =>
        normalizeString(projectTag).includes(normalizedTag)
      );
    });
  }

  if (season) {
    const seasonMapping = SEASONAL_PROJECTS[season as keyof typeof SEASONAL_PROJECTS];
    if (seasonMapping) {
      filtered = filtered.filter(project => {
        // Category matching with normalization
        const categoryMatch = seasonMapping.categories.some(seasonCategory =>
          normalizedStringCompare(project.category, seasonCategory)
        );

        // Tag matching with normalization
        const tagMatch = project.tags.some(projectTag =>
          seasonMapping.tags.some(seasonTag =>
            normalizeString(projectTag).includes(normalizeString(seasonTag))
          )
        );

        return categoryMatch || tagMatch;
      });
    }
  }

  return filtered;
};

/**
 * Get seasonal projects based on current season
 */
export const getSeasonalProjects = async (limit?: number): Promise<ProjectType[]> => {
  const currentSeason = getCurrentSeason();
  const filtered = await getFilteredProjects(undefined, undefined, undefined, currentSeason);

  // If we don't have enough seasonal projects, add some recent ones
  if (limit && filtered.length < limit) {
    const additionalProjects = recentProjects
      .filter(p => !filtered.some(f => f.id === p.id))
      .slice(0, limit - filtered.length);

    return [...filtered, ...additionalProjects];
  }

  return limit ? filtered.slice(0, limit) : filtered;
};

/**
 * Get unique project categories
 */
export const getProjectCategories = async (): Promise<string[]> => {
  return [...new Set(recentProjects.map(project => project.category))];
};

/**
 * Get unique project locations
 */
export const getProjectLocations = async (): Promise<string[]> => {
  return [...new Set(recentProjects.map(project => project.location))];
};

/**
 * Get unique project tags
 */
export const getProjectTags = async (): Promise<string[]> => {
  const allTags = recentProjects.flatMap(project => project.tags);
  return [...new Set(allTags)];
};

/**
 * Get all testimonials
 */
export const getTestimonials = async (): Promise<TestimonialType[]> => {
  return testimonials;
};

/**
 * Get testimonials filtered by rating
 */
export const getFilteredTestimonials = async (rating?: number): Promise<TestimonialType[]> => {
  if (rating) {
    return testimonials.filter(testimonial => testimonial.rating === rating);
  }
  return testimonials;
};

/**
 * Get testimonial rating counts
 */
export const getTestimonialRatingCounts = async (): Promise<Record<number, number>> => {
  const counts: Record<number, number> = {};
  testimonials.forEach(testimonial => {
    counts[testimonial.rating] = (counts[testimonial.rating] || 0) + 1;
  });
  return counts;
};

/**
 * Get service areas
 */
export const getServiceAreas = async (): Promise<ServiceArea[]> => {
  return SERVICE_AREAS;
};

/**
 * Get season display name
 */
export const getSeasonDisplayName = (season: string): string => {
  switch (season) {
    case 'vår': return 'våren';
    case 'sommer': return 'sommeren';
    case 'høst': return 'høsten';
    case 'vinter': return 'vinteren';
    default: return season;
  }
};