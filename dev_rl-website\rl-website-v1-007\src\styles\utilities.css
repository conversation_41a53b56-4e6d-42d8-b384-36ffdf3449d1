@layer utilities {
  /* Improved aspect ratios */
  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  /* Better container padding at different breakpoints */
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Improved text gradients */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-green-400;
  }

  /* Enhanced backdrop blur */
  .backdrop-blur {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Better transitions */
  .transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Improved shadows */
  .shadow-card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.05),
                0 12px 24px rgba(0,0,0,0.05);
  }

  /* Better animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }

  /* Improved grid layouts */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}