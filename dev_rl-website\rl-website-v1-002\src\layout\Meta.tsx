import React from "react";
import { Helmet } from "react-helmet-async";
import { SITE_CONFIG, CONTACT_INFO } from "../lib/constants";
import { generateSEOMeta } from "../lib/utils/seo";

interface MetaProps {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
    noindex?: boolean;
    schema?: Record<string, unknown>;
    keywords?: string[];
    location?: {
        city: string;
        region?: string;
    };
}

export const Meta: React.FC<MetaProps> = ({
    title,
    description = SITE_CONFIG.description,
    image = SITE_CONFIG.ogImage,
    type = "website",
    noindex = false,
    schema,
    keywords = SITE_CONFIG.keywords,
    location,
}) => {
    const fullTitle = title
        ? `${title} | ${SITE_CONFIG.name}`
        : location
        ? `Anleggsgartner i ${location.city} | ${SITE_CONFIG.name}`
        : SITE_CONFIG.name;

    const localDescription = location
        ? `Din lokale anleggsgartner i ${location.city}. Vi har inngående kjennskap til lokale forhold og leverer skreddersydde løsninger for ditt uterom.`
        : description;

    // Generate SEO meta tags using our utility
    const seoMeta = generateSEOMeta({
        title: fullTitle,
        description: localDescription,
        image,
        type,
        path: typeof window !== "undefined" ? window.location.pathname : "",
    });

    const baseSchema = {
        "@context": "https://schema.org",
        "@type": "LandscapingBusiness",
        name: SITE_CONFIG.name,
        image: SITE_CONFIG.ogImage,
        description: localDescription,
        "@id": SITE_CONFIG.url,
        url: SITE_CONFIG.url,
        telephone: SITE_CONFIG.phone,
        address: {
            "@type": "PostalAddress",
            streetAddress: CONTACT_INFO.address.street,
            addressLocality: CONTACT_INFO.address.city,
            postalCode: CONTACT_INFO.address.postalCode,
            addressRegion: CONTACT_INFO.address.county,
            addressCountry: "NO",
        },
        geo: {
            "@type": "GeoCoordinates",
            latitude: 60.0558,
            longitude: 10.2558,
        },
        areaServed: {
            "@type": "GeoCircle",
            geoMidpoint: {
                "@type": "GeoCoordinates",
                latitude: 60.0558,
                longitude: 10.2558,
            },
            geoRadius: "25000",
        },
        openingHoursSpecification: {
            "@type": "OpeningHoursSpecification",
            dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            opens: "07:00",
            closes: "16:00",
        },
        priceRange: "$$",
        sameAs: [CONTACT_INFO.social.facebook],
        hasOfferCatalog: {
            "@type": "OfferCatalog",
            name: "Anleggsgartnertjenester i Hole og Ringerike",
            itemListElement: [
                {
                    "@type": "Offer",
                    itemOffered: {
                        "@type": "Service",
                        name: "Belegningsstein",
                        description:
                            "Profesjonell legging av belegningsstein tilpasset lokale forhold",
                    },
                },
            ],
        },
    };

    return (
        <Helmet>
            <title>{seoMeta.title}</title>
            {/* Apply all meta tags from our SEO utility */}
            {seoMeta.meta.map((meta, index) => (
                <meta key={`meta-${index}`} {...meta} />
            ))}
            {/* Apply all link tags from our SEO utility */}
            {seoMeta.link.map((link, index) => (
                <link key={`link-${index}`} {...link} />
            ))}
            {/* Keep existing functionality */}
            {keywords && keywords.length > 0 && (
                <meta name="keywords" content={keywords.join(", ")} />
            )}
            {noindex && <meta name="robots" content="noindex" />}
            {schema && (
                <script type="application/ld+json">
                    {JSON.stringify(schema)}
                </script>
            )}
            {!schema && (
                <script type="application/ld+json">
                    {JSON.stringify(baseSchema)}
                </script>
            )}
        </Helmet>
    );
};
