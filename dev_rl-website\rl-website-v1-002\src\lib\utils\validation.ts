/**
 * Form validation utilities
 */

/**
 * Validates an email address
 * @returns Error message if invalid, undefined if valid
 */
export const validateEmail = (email: string): string | undefined => {
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return "Ugyldig e-postadresse";
    }
};

/**
 * Validates a Norwegian phone number
 * @returns Error message if invalid, undefined if valid
 */
export const validatePhone = (phone: string): string | undefined => {
    if (!/^(\+47)?[2-9]\d{7}$/.test(phone.replace(/\s/g, ""))) {
        return "Ugyldig telefonnummer (8 siffer)";
    }
};

/**
 * Validates that a field is not empty
 * @returns Error message if empty, undefined if valid
 */
export const validateRequired = (value: string): string | undefined => {
    if (!value.trim()) {
        return "Dette feltet er påkrevd";
    }
};
