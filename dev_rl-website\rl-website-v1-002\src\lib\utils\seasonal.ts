/**
 * Seasonal utilities for determining current season
 */

/**
 * Gets the current season based on the month
 * @returns 'vinter', 'vår', 'sommer', or 'høst'
 */
export const getCurrentSeason = (): "vinter" | "vår" | "sommer" | "høst" => {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return "vår";
    if (month >= 5 && month <= 7) return "sommer";
    if (month >= 8 && month <= 10) return "høst";
    return "vinter";
};
