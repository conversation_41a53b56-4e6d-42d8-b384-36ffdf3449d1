/**
 * Path utility functions
 */

/**
 * Encode special characters in image paths to ensure they load correctly
 * This is especially important for paths with Norwegian characters like æ, ø, å
 * 
 * @param path The image path to encode
 * @returns The encoded image path
 */
export const encodeImagePath = (path: string): string => {
  if (!path) return '';
  
  // Split the path into segments
  const segments = path.split('/');
  
  // Encode each segment and join them back together
  const encodedPath = segments
    .map(segment => {
      // Don't encode empty segments or segments that don't contain special characters
      if (!segment || !/[æøåÆØÅ]/.test(segment)) {
        return segment;
      }
      return encodeURIComponent(segment);
    })
    .join('/');
  
  return encodedPath;
};
