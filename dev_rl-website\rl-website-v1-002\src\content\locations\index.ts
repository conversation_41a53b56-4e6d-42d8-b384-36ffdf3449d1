import { ServiceArea } from '../../lib/types';

/**
 * Service areas where Ringerike Landskap operates
 */
export const serviceAreas: ServiceArea[] = [
  {
    city: 'Røyse',
    distance: '0 km',
    description: 'V<PERSON>rt hovedkontor ligger på Røyse i Hole kommune.',
    isBase: true
  },
  {
    city: 'Hole',
    distance: '5 km',
    description: 'Vi dekker hele Hole kommune med alle våre tjenester.'
  },
  {
    city: 'Hønefoss',
    distance: '15 km',
    description: 'Vi utfører oppdrag i hele Hønefoss og omegn.'
  },
  {
    city: 'Ringerike',
    distance: '20 km',
    description: 'Vi dekker hele Ringerike kommune med alle våre tjenester.'
  },
  {
    city: 'Jevnaker',
    distance: '25 km',
    description: 'Vi tar oppdrag i Jevnaker kommune.'
  },
  {
    city: 'Sundvollen',
    distance: '10 km',
    description: 'Vi utfører alle typer anleggsgartnertjenester i Sundvollen.'
  }
];

/**
 * Get all service areas
 * @returns Array of service areas
 */
export function getServiceAreas(): ServiceArea[] {
  return serviceAreas;
}

/**
 * Get service area by city name
 * @param city City name
 * @returns Service area or undefined if not found
 */
export function getServiceAreaByCity(city: string): ServiceArea | undefined {
  return serviceAreas.find(area => area.city.toLowerCase() === city.toLowerCase());
}

/**
 * Get the base service area (where the company is located)
 * @returns Base service area
 */
export function getBaseServiceArea(): ServiceArea | undefined {
  return serviceAreas.find(area => area.isBase);
}

/**
 * Get service areas within a specific distance
 * @param maxDistance Maximum distance in km
 * @returns Array of service areas within the specified distance
 */
export function getServiceAreasWithinDistance(maxDistance: number): ServiceArea[] {
  return serviceAreas.filter(area => {
    const distance = parseInt(area.distance.split(' ')[0]);
    return distance <= maxDistance;
  });
}
